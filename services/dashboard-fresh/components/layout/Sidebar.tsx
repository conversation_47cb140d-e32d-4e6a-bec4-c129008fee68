import { User } from "../../utils/auth.ts";

interface SidebarProps {
  user?: User;
  currentPath: string;
}

interface NavItem {
  name: string;
  href: string;
  icon: string;
  current?: boolean;
  children?: NavItem[];
}

export default function Sidebar({ user, currentPath }: SidebarProps) {
  const navigation: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/',
      icon: 'home',
      current: currentPath === '/'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: 'chart-bar',
      current: currentPath.startsWith('/analytics'),
      children: [
        { name: 'Overview', href: '/analytics', icon: 'chart-line' },
        { name: 'D3 Dashboard', href: '/analytics/d3-dashboard', icon: 'chart-pie' },
        { name: 'Cohort Analysis', href: '/analytics/cohorts', icon: 'users' },
        { name: 'Attribution', href: '/analytics/attribution', icon: 'link' },
        { name: 'Real-time', href: '/analytics/realtime', icon: 'lightning-bolt' }
      ]
    },
    {
      name: 'Links',
      href: '/links',
      icon: 'link',
      current: currentPath.startsWith('/links')
    },
    {
      name: 'Campaigns',
      href: '/campaigns',
      icon: 'megaphone',
      current: currentPath.startsWith('/campaigns')
    },
    {
      name: 'Reports',
      href: '/reports',
      icon: 'document-text',
      current: currentPath.startsWith('/reports')
    },
    {
      name: 'Integrations',
      href: '/integrations',
      icon: 'puzzle-piece',
      current: currentPath.startsWith('/integrations')
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: 'cog',
      current: currentPath.startsWith('/settings')
    }
  ];

  const getIcon = (iconName: string) => {
    const icons: Record<string, string> = {
      'home': 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
      'chart-bar': 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      'chart-line': 'M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z',
      'chart-pie': 'M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z',
      'users': 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z',
      'link': 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1',
      'lightning-bolt': 'M13 10V3L4 14h7v7l9-11h-7z',
      'megaphone': 'M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2h3a1 1 0 011 1v10a1 1 0 01-1 1h-3v2a1 1 0 01-1 1H8a1 1 0 01-1-1v-2H4a1 1 0 01-1-1V5a1 1 0 011-1h3z',
      'document-text': 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
      'puzzle-piece': 'M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V5a1 1 0 011-1h3a1 1 0 001-1V4z',
      'cog': 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z'
    };
    return icons[iconName] || icons['home'];
  };

  return (
    <div class="hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-40 lg:w-64 lg:bg-white dark:bg-gray-900 lg:shadow-lg lg:border-r lg:border-gray-200 dark:border-gray-700 lg:pt-16 lg:block transition-colors">
      <div class="flex flex-col h-full">
        {/* User info */}
        {user && (
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
              <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
                <span class="text-sm font-medium text-primary-600 dark:text-primary-400">
                  {user.firstName.charAt(0)}{user.lastName.charAt(0)}
                </span>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {user.firstName} {user.lastName}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {user.companyName || user.role}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav class="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
          {navigation.map((item) => (
            <div key={item.name}>
              <a
                href={item.href}
                class={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                  item.current
                    ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-900 dark:text-primary-100 border-r-2 border-primary-500'
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100'
                }`}
              >
                <svg
                  class={`mr-3 h-5 w-5 ${
                    item.current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d={getIcon(item.icon)}
                  />
                </svg>
                {item.name}
              </a>

              {/* Sub-navigation */}
              {item.children && item.current && (
                <div class="ml-8 mt-1 space-y-1">
                  {item.children.map((child) => (
                    <a
                      key={child.name}
                      href={child.href}
                      class={`group flex items-center px-2 py-1 text-xs font-medium rounded-md transition-colors ${
                        currentPath === child.href
                          ? 'bg-primary-50 dark:bg-primary-900/10 text-primary-700 dark:text-primary-300'
                          : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-700 dark:hover:text-gray-300'
                      }`}
                    >
                      <svg
                        class={`mr-2 h-4 w-4 ${
                          currentPath === child.href ? 'text-primary-500' : 'text-gray-400'
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d={getIcon(child.icon)}
                        />
                      </svg>
                      {child.name}
                    </a>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>

        {/* Footer */}
        <div class="px-4 py-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="text-xs text-gray-500 dark:text-gray-400">
              Fresh Dashboard v1.0
            </div>
            <a
              href="/auth/logout"
              class="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              Sign out
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
