import { FreshContext } from "$fresh/server.ts";
import { verifyJWT } from "../utils/auth.ts";
import { getCookies } from "$std/http/cookie.ts";
import { AppState } from "../types/fresh.ts";

export async function handler(
  req: Request,
  ctx: FreshContext<AppState>,
) {
  // Handle source map requests silently to prevent 404 errors in dev tools
  if (ctx.url.pathname.endsWith('.map')) {
    return new Response('', { status: 404 });
  }

  // Skip auth for public routes and static assets
  const publicRoutes = ['/auth/login', '/auth/register', '/auth/forgot-password', '/api/auth/login', '/api/auth/register', '/api/health'];
  const staticAssetPaths = ['/_frsh/', '/favicon.ico', '/static/', '/styles.css'];

  const isPublicRoute = publicRoutes.some(route => ctx.url.pathname.startsWith(route));
  const isStaticAsset = staticAssetPaths.some(path => ctx.url.pathname.startsWith(path));

  if (isPublicRoute || isStaticAsset) {
    return ctx.next();
  }

  // Development mode: Create a mock user for testing
  const isDevelopment = Deno.env.get("DENO_ENV") !== "production";
  if (isDevelopment && !ctx.state.isAuthenticated) {
    ctx.state.user = {
      id: "00000000-0000-0000-0000-000000000001",
      email: "<EMAIL>",
      firstName: "Test",
      lastName: "User",
      companyName: "Test Company",
      role: "admin",
      tenant_id: "00000000-0000-0000-0000-000000000001",
      isActive: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    ctx.state.isAuthenticated = true;
    return ctx.next();
  }

  // Try to get token from Authorization header or cookie
  let token: string | null = null;
  
  const authHeader = req.headers.get("Authorization");
  if (authHeader?.startsWith("Bearer ")) {
    token = authHeader.substring(7);
  } else {
    // Fallback to cookie for browser requests
    const cookies = getCookies(req.headers);
    token = cookies.auth_token || null;
  }

  if (token) {
    try {
      const user = await verifyJWT(token);
      ctx.state.user = user;
      ctx.state.isAuthenticated = true;
    } catch (error) {
      console.error("Auth verification failed:", error);
      ctx.state.user = null;
      ctx.state.isAuthenticated = false;
    }
  } else {
    ctx.state.user = null;
    ctx.state.isAuthenticated = false;
  }

  // Redirect unauthenticated users to login for protected routes
  if (!ctx.state.isAuthenticated && !isPublicRoute && !isStaticAsset) {
    // For API routes, return 401
    if (ctx.url.pathname.startsWith('/api/')) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }
    
    // For page routes, redirect to login
    const loginUrl = `/auth/login?redirect=${encodeURIComponent(ctx.url.pathname)}`;
    return new Response("", {
      status: 302,
      headers: { Location: loginUrl },
    });
  }

  return ctx.next();
}
